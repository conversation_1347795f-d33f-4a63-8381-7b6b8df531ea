#basic
sof.version=${sof_core_version}
app.name=shared-mobility-asr-core
app.version=*******
app.type=${project_type}
app.root=${user.home}/${app.name}
sof-env=predeploy

#db
uniform.env=stage
uniform.skyCode=groundtravel.shared.mobility.asr.core
uniform.dbName=TCTravelCarAsr

#dubbo
dubbo.application.name=shared-mobility-asr-core
dubbo.registry.address=t.dsf2.17usoft.com
dubbo.container=spring,log4j
dubbo.service.deploy.container=tomcat
dubbo.port=${PORT1}
dubbo.tcdsfGroup.gsName=dsf.group.name
dubbo.tcdsfGroup.version=dsf.group.version

#turbomq\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709mq\uFF0C\u53EF\u4EE5\u5220\u9664
mq.nameSrvAddress=mqnameserver.t.17usoft.com:9876

#drm\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709drm\uFF0C\u53EF\u4EE5\u5220\u9664
conf.domain=shared-mobility-asr-core
zkconnect=public1.zk.t.17usoft.com:2181
rootPath=flight

#redis\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709redis\uFF0C\u53EF\u4EE5\u5220\u9664
redis.groupName=redisGroupName

#kafka\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709kafka\uFF0C\u53EF\u4EE5\u5220\u9664
kfk.projectName=kfkProjectName
kfk.projectCode=kfkProjectCode

#\u77ED\u4FE1\u914D\u7F6E\uFF0C\u9879\u76EE\u6CA1\u6709\u77ED\u4FE1\u53D1\u9001\uFF0C\u53EF\u4EE5\u5220\u9664
mq.goto.message.topic=flight_gotocore_tp_message
mq.goto.message.code=flight_e_gotocore_message_template_send

#sso
sec.interface.url=http://authority.17usoft.com/Interface/Service.ashx
sec.403.url=/
sec.index.url=/index
sec.interface.projectCode=yourProjectCode
sec.sso.url=http://flightadminapi.t.17usoft.com/sso
sec.currentSys.url=http://flightadminapi.t.17usoft.com/shared-mobility-asr-core
session.timeout=3600

#http client
http_read_timeout=5000
connect_timeout=5000

car.mng.url=http://tcwireless.t.17usoft.com/carmng/

dsf.car.shared.mobility.order.service.gsName=dsf.car.order.service
dsf.car.shared.mobility.order.service.version=latest

labrador.shared.car.order.url=http://servicegw.t.ly.com/gateway/sharedcar/stage2
labrador.shared.car.order.token=e5e5e89f-2529-4012-b947-230501b2c962

approve.apply.detail.url=http://tcwireless.t.17usoft.com/supply/index/
marketing.mng2.url=http://marketing.travel.t.17usoft.com/marketingmng2

#crm-client
travel.mobility.supply.crm.core.dsf.gsName=dsf.car.shared.mobility.supply.crm.core

#语音识别服务地址
funasr.server.url=ws://10.178.121.126:10096
funasr.server.urls=ws://10.178.121.126:11091,ws://10.178.121.126:11092,ws://10.178.121.126:11093,ws://10.178.121.126:10096
#音视频临时文件保存路径
upload.file.temp.path=/data/temp/asr_files/

# car.order.service
car.order.service.gsName=dsf.car.order.service
car.order.service.version=1.2.1.4
