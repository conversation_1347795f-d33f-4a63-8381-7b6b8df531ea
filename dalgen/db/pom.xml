<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.ly.flight.dalgen</groupId>
    <artifactId>generator</artifactId>
    <packaging>pom</packaging>

    <name>LY DALGEN Generator Parent POM</name>
    <version>1.0</version>

    <organization>
        <name>LY-DALGEN-Generator</name>
        <url>http://www.ly.com</url>
    </organization>

    <properties>
        <h2.version>1.2.135</h2.version>
        <slf4j.version>1.6.0</slf4j.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <!-- ������ģ�� -->
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-core</artifactId>
            <version>4.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-generator-template</artifactId>
            <version>4.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-dal-ibatis</artifactId>
            <version>2.4.4.32</version>
        </dependency>

        <!-- generator-engine -->
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-generator</artifactId>
            <version>4.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.googlecode.rapid-framework</groupId>
            <artifactId>rapid-generator-ext</artifactId>
            <version>4.0.6</version>
        </dependency>

        <!-- apache-commons BEGIN -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.8.3</version>
        </dependency>
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.5</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.2.1</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>1.4</version>
        </dependency>
        <!-- apache-commons END -->

        <!-- logging BEGIN -->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
            <version>${slf4j.version}</version>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.16</version>
        </dependency>
        <!-- logging end -->

        <!-- template engine -->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.16</version>
        </dependency>

        <!-- jdbc drivers begin -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>5.1.38</version>
        </dependency>
        <!-- jdbc drivers end -->

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.groovy.maven</groupId>
                <artifactId>gmaven-plugin</artifactId>
                <version>1.0</version>
                <configuration>
                    <source>${pom.basedir}/gen.groovy</source>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.ly.flight.intl.maven.plugins</groupId>
                <artifactId>sof-dalgen-maven-plugin</artifactId>
                <version>1.2</version>
                <configuration>
                    <configXmlFiles>
                        <!-- ��ͬ���ݿ�������ö���ļ� -->
                        <configXmlFile>gen_config.xml</configXmlFile>
                    </configXmlFiles>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.6</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <echo>
                                    settings.localRepository=${settings.localRepository}
                                </echo>
                                <ant antfile="build.xml" target="${antTarget}" inheritall="true" inheritrefs="true">
                                    <property name="maven.compile.classpath" refid="maven.compile.classpath"/>
                                    <property name="genInputCmd" value="${genInputCmd}"/>
                                    <property name="mvn_localRepository" value="${settings.localRepository}"/>
                                </ant>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <repositories>
                <repository>
                    <id>ly-public</id>
                    <name>LY Public Repository</name>
                    <url>http://nexus.17usoft.com/repository/mvn-all/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>ly-release</id>
                    <name>LY Release Repository</name>
                    <url>http://nexus.17usoft.com/repository/mvn-flight-release/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>ly-central</id>
                    <name>LY Central Repository</name>
                    <url>http://nexus.17usoft.com/repository/mvn-all/</url>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>ly-snapshots</id>
                    <name>LY SNAPSHOTS Repository</name>
                    <url>http://nexus.17usoft.com/repository/mvn-flight-snapshot/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>public</id>
                    <url>http://nexus.17usoft.com/repository/mvn-all/</url>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

</project>
