#!/usr/bin/env bash
# xml配置文件路径
CONFIG_XML_PATH="../gen_config.xml"
# form配置文件路径
FORM_CONFIG_XML_PATH="../form_config.xml"
# 输出路径
OUTPUT="./output/"

ROT="$(pwd)"
XML_PATH="$(pwd)/sof-form-gen/output/$2/$2.xml"
GEN_PATH="$(pwd)/sof-form-gen/output/"
GEN_XML_PATH="$(pwd)/tables/$2.xml"

if [ ! -d "./sof-form-gen/" ]; then
  rm -rf sof-form-*
  # program : https://git.17usoft.com/flightint/develop-toolkit
  curl -O "http://ebk.qa.17u.cn/guojijipiao/sof-form-gen.zip"
  unzip -o sof-form-gen.zip
  chmod +x ./sof-form-gen/gen-jar.sh
fi
cd sof-form-gen
./gen-jar.sh $OUTPUT $2 ./templates/ $CONFIG_XML_PATH $FORM_CONFIG_XML_PATH
cd $ROT
if [ ! -f $GEN_XML_PATH ]; then
  cp $XML_PATH $GEN_XML_PATH
  mvn groovy:execute -DgeneratorConfigFile=gen_config.xml -DexecuteTarget=dal -DgenInputCmd=$2
  exit 8
fi

mvn groovy:execute -DgeneratorConfigFile=gen_config.xml -DexecuteTarget=$1 -DgenInputCmd=$2

echo "[提示][sof-gen]form相关生成完成,生成路径：$GEN_PATH"
echo "[提示][sof-gen]*lowcode-config.json 导入SOF-FORM-GENERATE http://gen.fly.17usoft.com/ 手动配置edit表单"
echo "[提示][sof-gen]*lowcode-config.json 导入SOF-FORM-GENERATE http://gen.fly.17usoft.com/ 手动配置edit表单"
echo "[提示][sof-gen]*lowcode-config.json 导入SOF-FORM-GENERATE http://gen.fly.17usoft.com/ 手动配置edit表单"
echo "[提示][sof-gen]*lowcode-config.json 导入SOF-FORM-GENERATE http://gen.fly.17usoft.com/ 手动配置edit表单"
echo "[提示][sof-gen]*lowcode-config.json 导入SOF-FORM-GENERATE http://gen.fly.17usoft.com/ 手动配置edit表单"
