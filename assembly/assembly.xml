<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.3 http://maven.apache.org/xsd/assembly-1.1.3.xsd">
    <id>assembly</id>

    <formats>
        <format>dir</format>
        <format>war</format>
    </formats>

    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${basedir}/../target/${assembly.name}.sof/webdocs</directory>
            <includes>
                <include>**/*.*</include>
            </includes>
            <outputDirectory>./</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../target/${assembly.name}.sof/config</directory>
            <includes>
                <include>**/*.xml</include>
                <include>**/*.properties</include>
            </includes>
            <excludes>
                <exclude>**/dsf_application.properties</exclude>
                <exclude>**/tcbase.properties</exclude>
                <exclude>**/inb.properties</exclude>
            </excludes>
            <outputDirectory>WEB-INF/</outputDirectory>
        </fileSet>
        <fileSet>
            <directory>${basedir}/../target/${assembly.name}.sof/config</directory>
            <includes>
                <include>**/dsf_application.properties</include>
                <include>**/tcbase.properties</include>
                <include>**/inb.properties</include>
                <include>**/log4j2.xml</include>
                <include>**/log4j2.component.properties</include>
            </includes>
            <outputDirectory>WEB-INF/classes</outputDirectory>
        </fileSet>
    </fileSets>

    <moduleSets>
        <moduleSet>
            <useAllReactorProjects>true</useAllReactorProjects>

            <excludes>
                <exclude>${project.groupId}:*test</exclude>
            </excludes>

            <binaries>
                <outputDirectory>WEB-INF/lib</outputDirectory>
                <unpack>false</unpack>
                <includeDependencies>true</includeDependencies>
                <dependencySets>
                    <dependencySet>
                        <outputDirectory>WEB-INF/lib</outputDirectory>
                        <excludes>
                            <exclude>${project.groupId}:${assembly.name}*</exclude>
                        </excludes>
                    </dependencySet>
                </dependencySets>
            </binaries>
        </moduleSet>
    </moduleSets>
</assembly>