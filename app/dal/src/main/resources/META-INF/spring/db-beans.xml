<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context.xsd"
	   default-autowire="byName">

	<context:annotation-config/>
	<context:component-scan base-package="com.ly.travel.shared.mobility.dal"/>
	<aop:aspectj-autoproxy proxy-target-class="true"/>

	<!-- Only one tx:annotation-driven can be marked in the Spring 4 Container.
         If you have configured more than one TransactionManagers, please use "qualifier" to mark your TransactionManager. -->
	<tx:annotation-driven/>

	<bean id="travelAsrDatasource" class="com.ly.dal.datasource.RoutableDataSource" init-method="init"
		  destroy-method="close">
		<property name="env" value="${uniform.env}"/>
		<property name="projectId" value="${uniform.skyCode}"/>
		<property name="dbName" value="${uniform.dbName}"/>
	</bean>

	<bean id="travelAsrDatasourceProxy" class="org.springframework.jdbc.datasource.LazyConnectionDataSourceProxy">
		<property name="targetDataSource" ref="travelAsrDatasource"/>
	</bean>

	<bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="travelAsrDatasourceProxy"/>
	</bean>

	<bean id="travelAsrDatasourceTransactionTemplate" class="org.springframework.transaction.support.TransactionTemplate">
		<property name="transactionManager">
			<ref bean="transactionManager"/>
		</property>
	</bean>

	<bean id="travelAsrSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
		<property name="dataSource" ref="travelAsrDatasourceProxy"/>
		<property name="globalConfig" ref="commonGlobalConfig"/>
		<property name="mapperLocations" value="classpath:mybatisplus/travelcarasr/*.xml"/>

		<!-- 插件注册 -->
		<property name="plugins">
			<list>
				<bean class="com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor">
					<property name="interceptors">
						<list>
							<!-- 注册分页插件 -->
							<bean class="com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor">
								<property name="dbType" value="MYSQL"/>
							</bean>
							<bean class="com.baomidou.mybatisplus.extension.plugins.inner.OptimisticLockerInnerInterceptor">
							</bean>

						</list>
					</property>
				</bean>
			</list>
		</property>
	</bean>


	<bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
		<property name="basePackage" value="com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper"/>
		<property name="sqlSessionFactoryBeanName" value="travelAsrSessionFactory"/>
	</bean>
	<bean id="commonFieldHandler" class="com.ly.travel.shared.mobility.dal.mybatisplus.config.CommonFieldHandler"/>

	<bean id="commonGlobalConfig" class="com.baomidou.mybatisplus.core.config.GlobalConfig">
		<property name="metaObjectHandler" ref="commonFieldHandler"/>
	</bean>


</beans>
