<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.mapper.AsrRecognitionRecordMapper">

    <update id="invalidRecords" parameterType="map">
        UPDATE asr_recognition_record
        SET status = -1
        WHERE order_no = #{taskId} AND env = #{env}
    </update>

    <select id="listValidRecords" resultType="com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrRecognitionRecordDO">
        SELECT *
        FROM asr_recognition_record
        WHERE order_no = #{taskId}
          AND env = #{env}
          AND status = 0
        ORDER BY seq_no ASC
    </select>

</mapper>
