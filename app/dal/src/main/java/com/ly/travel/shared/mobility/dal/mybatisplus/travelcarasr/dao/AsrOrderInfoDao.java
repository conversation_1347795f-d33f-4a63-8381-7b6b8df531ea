package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ly.sof.utils.page.PageQuery;
import com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model.AsrOrderInfoDO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * asr订单信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/19 10:05
 */
public interface AsrOrderInfoDao extends IService<AsrOrderInfoDO> {
    Page<AsrOrderInfoDO> queryPage(PageQuery pageQuery, String mobileNo, String orderSerialNo, String plateNo, Integer uploadStatus, Integer auditStatus, Date tripStartTime,Date tripEndTime);

    AsrOrderInfoDO queryByOrderSerialNo(String orderSerialNo);

    List<AsrOrderInfoDO> queryByMobileNo(String mobileNo);
}
