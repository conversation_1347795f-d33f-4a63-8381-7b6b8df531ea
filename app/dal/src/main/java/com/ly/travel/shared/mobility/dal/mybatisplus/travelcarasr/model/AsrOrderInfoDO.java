package com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr.model;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * asr订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025/08/19 10:05
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("asr_order_info")
public class AsrOrderInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 环境
     */
    @TableField(fill = FieldFill.INSERT)
    private String env;

    /**
     * 订单流水号
     */
    @TableField("order_serial_no")
    private String orderSerialNo;

    /**
     * 手机号
     */
    @TableField("mobile_no")
    private String mobileNo;

    /**
     * 行程开始时间
     */
    @TableField("trip_start_time")
    private Date tripStartTime;

    /**
     * 行程结束时间
     */
    @TableField("trip_end_time")
    private Date tripEndTime;

    /**
     * 上传时间
     */
    @TableField("upload_time")
    private Date uploadTime;

    /**
     * 司机姓名
     */
    @TableField("driver_name")
    private String driverName;

    /**
     * 车牌号
     */
    @TableField("plate_no")
    private String plateNo;

    /**
     * 上传状态
     */
    @TableField("upload_status")
    private Integer uploadStatus;

    /**
     * 审核状态
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 审核意见
     */
    @TableField("audit_comment")
    private String auditComment;

    /**
     * 司机是否违规 0:否 1:是
     */
    @TableField("driver_violated")
    private Integer driverViolated;

    /**
     * 上传文件地址
     */
    @TableField("file_url")
    private String fileUrl;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 操作人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 司机服务评分
     */
    @TableField("driver_score")
    private BigDecimal driverScore;


}
