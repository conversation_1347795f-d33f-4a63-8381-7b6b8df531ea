package com.ly.travel.shared.mobility.dal.mybatisplus.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Value;

import java.util.Date;

@Slf4j
public class CommonFieldHandler implements MetaObjectHandler {
    @Value("${sof-env}")
    private String env;

    @Override
    public void insertFill(MetaObject metaObject) {
        if( metaObject.hasGetter("env")) {
            Object orgEnv = metaObject.getValue("env");
            if (orgEnv == null) {
                this.setFieldValByName("env", env, metaObject);
            }
        }
        if( metaObject.hasGetter("createTime")) {
            Object orgCreateTime = metaObject.getValue("createTime");
            if (orgCreateTime == null) {
                this.setFieldValByName("createTime", new Date(), metaObject);
            }
        }
        if( metaObject.hasGetter("createBy")) {
            String orgCreateBy = (String) metaObject.getValue("createBy");
            if (StringUtils.isBlank(orgCreateBy)) {
                this.setFieldValByName("createBy", "AsrCore", metaObject);
            }
        }
        if( metaObject.hasGetter("updateTime")) {
            Object orgUpdateTime = metaObject.getValue("updateTime");
            if (orgUpdateTime == null) {
                this.setFieldValByName("updateTime", new Date(), metaObject);
            }
        }

        if( metaObject.hasGetter("updateBy")) {
            String orgUpdateBy = (String)metaObject.getValue("updateBy");
            if (StringUtils.isBlank(orgUpdateBy)) {
                this.setFieldValByName("updateBy", "AsrCore", metaObject);
            }
        }

    }

    @Override
    public void updateFill(MetaObject metaObject) {
        if( metaObject.hasGetter("updateTime")) {
            this.setFieldValByName("updateTime", new Date(), metaObject);
        }
        if( metaObject.hasGetter("updateBy")) {
            this.setFieldValByName("updateBy", "AsrCore", metaObject);
        }
    }
}
