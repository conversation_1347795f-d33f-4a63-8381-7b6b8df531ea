package com.ly.travel.shared.mobility.dal.mybatisplus;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.OutputFile;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.rules.DateType;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.baomidou.mybatisplus.generator.fill.Column;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

public class TCTravelCarAsrGenerator {
    //修改为你自己的数据库地址
    private static final String datasourceUrl = "********************************************************************************************************************************";
    //修改为你自己的数据库账号
    private static final String datasourceUsername = "TCTravelCarAsr";
    //修改为你自己的数据库密码
    private static final String datasourcePassword = "6AdfOM0Bl36X7MDTJKvBnR8l9qlOLn9N";

    private static String suffix = StringUtils.EMPTY;


    public static void main(String[] args) {
        // 项目路径
        String projectPath = System.getProperty("user.dir");

        // 代码生成器
        FastAutoGenerator fastAutoGenerator = FastAutoGenerator.create(datasourceUrl, datasourceUsername, datasourcePassword)
                // 全局配置
                .globalConfig(initGlobalConfig(projectPath))
                .packageConfig(initPackageConfig(projectPath))
                .strategyConfig(initStrategyConfig())
                // 使用Freemarker引擎模板，默认的是Velocity引擎模板
                .templateEngine(new FreemarkerTemplateEngine())
                .templateConfig(templateConfig -> templateConfig.controller(""));
        if (StringUtils.isNotBlank(suffix)) {
            fastAutoGenerator.templateConfig(templateConfig -> templateConfig.service(""));
            fastAutoGenerator.templateConfig(templateConfig -> templateConfig.serviceImpl(""));
            fastAutoGenerator.templateConfig(templateConfig -> templateConfig.mapper(""));
            fastAutoGenerator.templateConfig(templateConfig -> templateConfig.mapperXml(""));
        }
        fastAutoGenerator.execute();

    }

    // 处理 all 情况
    protected static List<String> getTables(String tables) {
        String[] split = tables.split(" ");
        String tarTables = split[0];
        if (split.length > 1) {
            suffix = split[1];
        }
        return "all".equals(tarTables) ? Collections.emptyList() : Arrays.asList(tarTables.split(","));
    }

    /**
     * 初始化全局配置
     */
    private static Consumer<GlobalConfig.Builder> initGlobalConfig(String projectPath) {
        return builder -> {
            // 作者
            builder
                    //.author("pengmq")
                    // 输出路径
                    .outputDir(projectPath + "/app/dal/src/main/java")
                    // 禁止打开输出目录
                    .disableOpenDir()
                    // 开启swagger
                    //.enableSwagger()
                    // 注释日期
                    .commentDate("yyyy/MM/dd HH:mm")
                    .dateType(DateType.ONLY_DATE)
                    // 开启覆盖之前生成的文件
                    .fileOverride();
        };
    }

    /**
     * 初始化包配置
     */
    private static Consumer<PackageConfig.Builder> initPackageConfig(String projectPath) {
        return builder -> {
            builder.parent("com.ly.travel.shared.mobility.dal.mybatisplus.travelcarasr") //生成文件的父包
                    .entity("model")
                    .service("dao")
                    .serviceImpl("dao.impl")
                    .controller("")
                    .mapper("mapper")
                    .xml("mapper")
                    .pathInfo(Collections.singletonMap(OutputFile.mapperXml, projectPath + "/app/dal/src/main/resources/mybatisplus/travelcarasr"));
        };
    }

    /**
     * 初始化策略配置
     */
    private static BiConsumer<Function<String, String>, StrategyConfig.Builder> initStrategyConfig() {
        return (scanner, builder) -> {
            builder.addInclude(getTables(scanner.apply("请输入表名,多个英文逗号分隔,所有输入 all,举例: table_xxx1,table_xxx2")))
                    // 增加过滤表前缀
                    //.addTablePrefix("t_")
                    // service策略配置
                    .serviceBuilder()
                    .formatServiceFileName("%sDao" + suffix)
                    .formatServiceImplFileName("%sDaoImpl" + suffix)
                    // entity策略配置
                    .entityBuilder()
                    .formatFileName("%sDO" + suffix)
                    // 数据库表映射到实体的命名策略
                    .naming(NamingStrategy.underline_to_camel)
                    // 数据库表字段映射到实体的命名策略
                    .columnNaming(NamingStrategy.no_change)
                    // 开启lombok模型
                    .enableLombok()
                    .enableChainModel()
                    .addTableFills(new Column("create_time", FieldFill.INSERT)) // 自动填充配置  create_time  update_time 两种方式
                    .addTableFills(new Column("update_time", FieldFill.INSERT_UPDATE))
                    .addTableFills(new Column("create_by", FieldFill.INSERT))
                    .addTableFills(new Column("update_by", FieldFill.INSERT_UPDATE))
                    .addTableFills(new Column("v", FieldFill.INSERT)) // 自动填充配置  create_time  update_time 两种方式
                    .addTableFills(new Column("env", FieldFill.INSERT))
                    // controller策略设置
                    .controllerBuilder()
                    .formatFileName("%sController")
//                    .enableRestStyle()
//                    .enableHyphenStyle()
                    // mapper策略设置
                    .mapperBuilder()
                    // 生成通用的resultMap
                    //.enableBaseResultMap()
                    //.enableBaseColumnList()
                    .superClass(BaseMapper.class)
                    .formatMapperFileName("%sMapper" + suffix)
                    .enableMapperAnnotation()
                    .formatXmlFileName("%sMapper" + suffix)
            ;
            builder.addExclude().controllerBuilder();
        };
    }
}
