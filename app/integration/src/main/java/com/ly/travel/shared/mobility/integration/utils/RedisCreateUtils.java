package com.ly.travel.shared.mobility.integration.utils;

import com.alibaba.fastjson2.JSON;
import com.ly.tcbase.config.ConfigCenterClient;
import lombok.Data;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.Connection;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisCluster;

import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;

public class RedisCreateUtils {
    public final static String CONFIG_CENTER_KEY = "TCBase.Cache.v2";

    public static JedisCluster jedisCluster(String redisName) throws Exception {

        String s = ConfigCenterClient.get(CONFIG_CENTER_KEY);

        List<CacheConfig> cacheConfigList = JSON.parseArray(s, CacheConfig.class);

        // proxy mode
        CacheConfig typeSConfig = cacheConfigList.stream()
                .filter(config -> config.getName().equals(redisName))
                .filter(config -> config.getType().equals("S"))
                .findFirst().orElse(null);
        if (typeSConfig != null) {
            return doCreateJedisCluster(typeSConfig);
        }

        // ip mode type c
        CacheConfig typeCConfig = cacheConfigList.stream()
                .filter(config -> config.getName().equals(redisName))
                .filter(config -> config.getType().equals("C"))
                .findFirst().orElse(null);

        if (typeCConfig != null) {
            return doCreateJedisCluster(typeCConfig);
        }
        // error
        return null;
    }


    private static JedisCluster doCreateJedisCluster(CacheConfig config) {
        GenericObjectPoolConfig<Connection> poolConfig = new GenericObjectPoolConfig<>();
        poolConfig.setMinIdle(4);
        poolConfig.setMaxIdle(12);
        poolConfig.setMaxTotal(20);
        String password = "";
        Set<HostAndPort> nodes = new LinkedHashSet<>();
        for (CacheConfig.Node instance : config.getInstances()) {
            password = instance.getPassword();
            String[] hap = instance.getIp().split(":");
            nodes.add(new HostAndPort(hap[0], Integer.parseInt(hap[1])));
        }
        return new JedisCluster(nodes, 2000, 2000, 5, password, poolConfig);
    }

    @Data
    public static class CacheConfig {
        private List<Node> instances = new LinkedList<>();
        private String name;
        private String type;

        @Data
        public static class Node {
            private String ip;
            private String password;
            private String sentinel;
        }
    }
}
