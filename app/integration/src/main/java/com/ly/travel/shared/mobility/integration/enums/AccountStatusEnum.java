package com.ly.travel.shared.mobility.integration.enums;

import lombok.Getter;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/18
 */
@Getter
public enum AccountStatusEnum {

    INVALID(0,"无效"),
    WAIT_SEND(1,"待分发"),
    WAIT_COMPLETE(2,"待完善"),
    WAIT_SHIP(3,"待邮寄"),
    WAIT_PERFORM(4,"待履约"),
    PERFORMED(5,"已履约")
    ;

    private final Integer status;

    private final String desc;

    AccountStatusEnum(Integer status, String desc){
        this.status = status;
        this.desc = desc;
    }
}
