package com.ly.travel.shared.mobility.integration.configcenter;

import com.ly.tcbase.config.ConfigCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> By Houce
 * @since 2025/8/18
 */
@Service("configCenterRepository")
@Slf4j
public class ConfigCenterRepository {

    public String getValue(String key) {
        try {
            return ConfigCenterClient.get(key);
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    public String getValue(String domain, String key) {
        try {
            return ConfigCenterClient.get(domain, key);
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取配置，若有则取配置值，没配返回默认值
     * @param key 配置key
     * @param def 默认值
     * @return value
     */
    public String getWithDefaultValue(String key, String def) {
        try {
            String value = ConfigCenterClient.get(key);
            return StringUtils.isBlank(value) ? def : value;
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            return def;
        }
    }
}
