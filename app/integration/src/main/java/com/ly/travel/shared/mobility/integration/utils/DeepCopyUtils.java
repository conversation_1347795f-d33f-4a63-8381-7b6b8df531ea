package com.ly.travel.shared.mobility.integration.utils;

import lombok.extern.slf4j.Slf4j;
import org.dozer.DozerBeanMapper;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class DeepCopyUtils {
    private static final DozerBeanMapper DOZER = new DozerBeanMapper();

    /**
     * 把source中的值复制到target
     */
    public static void copy(Object source, Object target) {
        if (null == source || null == target) {
            return;
        }
        DOZER.map(source, target);
    }

    public static <T> T map(Object source, Class<T> tarClass) {
        if (null == source) {
            return null;
        }
        return DOZER.map(source, tarClass);
    }

    /**
     * @param s   数据对象
     * @param clz 复制目标类型
     * @param <T>
     * @param <S>
     * @return
     * @Description: list深度复制
     */
    public static <T, S> List<T> copyList(List<S> s, Class<T> clz) {
        if (s == null) {
            return null;
        }
        List<T> list = new ArrayList<T>();
        for (S vs : s) {
            list.add(DOZER.map(vs, clz));
        }
        return list;
    }


    /**
     * 合并两个对象,将主对象的null 字段 用次对象的字段覆盖
     *
     * @param mainObj  主对象,如果主对象和次对象的相同字段都有值的话,将保留主对象值
     * @param minorObj 次对象,如果主对象和次对象的相同字段都有值的话,将保留主对象值
     * @param <T>
     * @return
     */
    public static <T> T mergeObjects(T mainObj, T minorObj) {
        if (mainObj == null && minorObj == null) {
            return null;
        }
        Class objClass = null;
        if (mainObj != null) {
            objClass = mainObj.getClass();
        } else {
            objClass = minorObj.getClass();
        }

        if (mainObj == null) {
            T tarObj2 = null;
            try {
                tarObj2 = (T) objClass.newInstance();
            } catch (Exception e) {
                log.error("", e);
            }
            copy(minorObj, tarObj2);
            return tarObj2;
        }
        if (minorObj == null) {
            T tarObj1 = null;
            try {
                tarObj1 = (T) objClass.newInstance();
            } catch (Exception e) {
                log.error("", e);
            }
            copy(mainObj, tarObj1);
            return tarObj1;
        }

        T tarObj = null;
        try {
            tarObj = (T) objClass.newInstance();
        } catch (Exception e) {
            log.error("", e);
        }
        copy(minorObj, tarObj);

        Field[] fields = objClass.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            field.setAccessible(true);

            try {
                //主对象字段为null的话,才用次对象的值覆盖
                if (field.get(mainObj) == null) {
                    field.set(tarObj, field.get(minorObj));
                } else {
                    field.set(tarObj, field.get(mainObj));
                }
            } catch (Exception e) {
                log.error("", e);
            }
        }
        return tarObj;
    }

}
