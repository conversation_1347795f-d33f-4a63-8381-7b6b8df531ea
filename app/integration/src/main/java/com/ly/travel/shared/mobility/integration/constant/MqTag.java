package com.ly.travel.shared.mobility.integration.constant;


public interface MqTag {
    /**
     * 分销商判责超时处理
     */
    String DISTRI_JUDGE_TIMEOUT = "distri_judge_timeout";

    /**
     * 供应商申诉超时处理
     */
    String SUPPLIER_APPEAL_TIMEOUT = "supplier_appeal_timeout";

    /**
     * 分销商风险单导入
     */
    String DISTRI_RISK_ORDER_IMPORT = "distri_risk_order_import";

    /**
     * 分销商申诉结果导入
     */
    String DISTRI_APPEAL_RESULT_IMPORT = "distri_appeal_result_import";

    /**
     * 供应商申诉导入
     */
    String SUPPLIER_APPEAL_IMPORT = "supplier_appeal_import";
}
