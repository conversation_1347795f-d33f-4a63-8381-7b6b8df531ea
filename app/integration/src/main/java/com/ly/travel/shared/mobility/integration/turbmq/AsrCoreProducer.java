package com.ly.travel.shared.mobility.integration.turbmq;

import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.sof.api.mq.producer.Producer;

import java.util.Date;

public interface AsrCoreProducer extends Producer {
    boolean sendDelayMQ(String topic, String eventCode, Object payload, Date delayTime, SerializeEnum serialize) ;

    boolean sendDelayMQ(String topic, String eventCode, Object payload, Date delayTime);

    boolean sendMQ(String topic, String eventCode, Object payload, SerializeEnum serialize) ;

    boolean sendMQ(String topic, String eventCode, Object payload) ;

}
