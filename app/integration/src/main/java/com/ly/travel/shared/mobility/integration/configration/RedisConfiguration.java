package com.ly.travel.shared.mobility.integration.configration;

import com.ly.travel.shared.mobility.integration.utils.RedisCreateUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisCluster;

@Configuration
public class RedisConfiguration {

    public static final String redisName = "GroundTravelAsrCore";


    @Bean(name = "jedisCluster")
    public JedisCluster jedisCluster() throws Exception {
        return RedisCreateUtils.jedisCluster(redisName);
    }
}

