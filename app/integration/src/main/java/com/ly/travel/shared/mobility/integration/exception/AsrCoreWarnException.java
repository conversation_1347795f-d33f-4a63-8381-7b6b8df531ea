package com.ly.travel.shared.mobility.integration.exception;


import com.ly.travel.shared.mobility.integration.enums.AsrCoreErrorEnum;

public class AsrCoreWarnException extends RuntimeException {
    private int code;
    private String message;

    public AsrCoreWarnException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public AsrCoreWarnException(String message, Throwable cause) {
        super(message, cause);
    }

    public AsrCoreWarnException(String message) {
        super(message);
        this.code = AsrCoreErrorEnum.BUSINESS_ERROR.getCode();
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
