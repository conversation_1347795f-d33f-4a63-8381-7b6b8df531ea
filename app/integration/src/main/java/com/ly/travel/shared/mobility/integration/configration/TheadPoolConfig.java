package com.ly.travel.shared.mobility.integration.configration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.atomic.AtomicInteger;


@Configuration
@Slf4j
public class TheadPoolConfig {

    private static final int CPU_COUNT = Runtime.getRuntime().availableProcessors();

    // 核心线程数大小
    private static final int CORE_POOL_SIZE = CPU_COUNT;

    // 最大线程数
    private static final int MAX_POOL_SIZE = CPU_COUNT * 2 + 1;

    // 阻塞队列
    private static final int QUEUE_SIZE = 10;

    // 存活时间
    private static final int KEEP_ALIVE = 30;

    private static final AtomicInteger atomicInteger = new AtomicInteger();


    @Bean(value = "carLogSaveTaskPool")
    public Executor carLogSaveTaskPool() {
        ThreadPoolTaskExecutor taskExecutor = new ThreadPoolTaskExecutor();
        //设置线程池参数信息
        taskExecutor.setCorePoolSize(20);
        taskExecutor.setMaxPoolSize(50);
        taskExecutor.setQueueCapacity(1000);
        taskExecutor.setKeepAliveSeconds(KEEP_ALIVE);
        taskExecutor.setThreadNamePrefix("carLogSaveTaskPool-");
        taskExecutor.setWaitForTasksToCompleteOnShutdown(true);
        taskExecutor.setAwaitTerminationSeconds(60);
        taskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        //初始化线程池
        taskExecutor.initialize();
        return taskExecutor;
    }

}
