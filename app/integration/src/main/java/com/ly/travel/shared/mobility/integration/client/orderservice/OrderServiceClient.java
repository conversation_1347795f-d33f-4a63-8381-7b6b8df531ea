package com.ly.travel.shared.mobility.integration.client.orderservice;

import com.alibaba.fastjson.JSON;
import com.ly.travel.car.orderservice.facade.IVRCarOrderFacade;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderDetailRequest;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderListRequest;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderDetailResponse;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderListResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


@Slf4j
@Service
public class OrderServiceClient {
    private static final String LOG_PREFIX = "OrderServiceClient";


    @Resource
    private IVRCarOrderFacade carIvrOrderFacade;

    public IVROrderListResponse queryIvrOrderList(IVROrderListRequest request) {
        log.info("{} queryIvrOrderList request:{}", LOG_PREFIX, JSON.toJSONString(request));
        IVROrderListResponse resp = carIvrOrderFacade.orderList(request);
        log.info("{} queryIvrOrderList response:{}", LOG_PREFIX, JSON.toJSONString(resp));
        return resp;

    }

    public IVROrderDetailResponse orderDetail(IVROrderDetailRequest request) {
        log.info("{} orderDetail request:{}", LOG_PREFIX, JSON.toJSONString(request));
        IVROrderDetailResponse resp = carIvrOrderFacade.orderDetail(request);
        log.info("{} orderDetail response:{}", LOG_PREFIX, JSON.toJSONString(resp));
        return resp;
    }
}
