package com.ly.travel.shared.mobility.integration.utils;

import com.ly.sof.utils.common.SerialNoGenerator;
import lombok.experimental.UtilityClass;

/**
 * 序列号工具类
 *
 * <AUTHOR>
 * @version Id:SerialNoUtil.java,v 0.1 2023/12/29 13:29 chengweiwen Exp $$
 */
@UtilityClass
public class SerialNoUtil {

    /** traceId前缀 */
    public static final String TRACE_ID_PREFIX = "CAR";

    /**
     * 生成 traceId
     *
     * @return traceId
     */
    public String genTraceId() {
        return SerialNoGenerator.generateSerialNo(TRACE_ID_PREFIX, 6);
    }
}
