package com.ly.travel.shared.mobility.integration.exception;


import com.ly.travel.shared.mobility.integration.enums.AsrCoreErrorEnum;

public class AsrCoreException extends RuntimeException {
    private int code;
    private String message;

    public AsrCoreException(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public AsrCoreException(String message, Throwable cause) {
        super(message, cause);
    }

    public AsrCoreException(String message) {
        super(message);
        this.code = AsrCoreErrorEnum.BUSINESS_ERROR.getCode();
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
