package com.ly.travel.shared.mobility.integration.utils;

import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.S3ClientOptions;
import com.amazonaws.services.s3.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR> By Houce
 * @since 2023/2/14
 */
@Component
@Slf4j
public class S3ClientApi {

    private AmazonS3Client s3client;

    private static final String BUCKET_NAME = "bucket-asr-core";
    /**
     * 私有访问地址域名
     */
    private static final String PRIVATE_URL = "oss.dss.17usoft.com";
    /**
     * 公有访问地址域名
     */
    private static final String PUBLIC_URL = "file.40017.cn";


    public AmazonS3Client getS3client() {

        if (s3client == null) {

            String accessKey = "kNjwL0xuM7El6V7Jubrn";
            String secretKey = "NjEmhg85apY4veef4qcsfrMn1gkKP3Cbcfq5sY1d";
            String endpoint = "http://oss.dss.17usoft.com";

            AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
            ClientConfiguration clientCfg = new ClientConfiguration();
            clientCfg.setProtocol(endpoint.startsWith(Protocol.HTTPS.toString()) ? Protocol.HTTPS : Protocol.HTTP);
            // 增加连接池配置
            clientCfg.setMaxConnections(100); // 增加最大连接数
            s3client = new AmazonS3Client(credentials, clientCfg);
            s3client.setEndpoint(endpoint);
            s3client.setS3ClientOptions(new S3ClientOptions().withPathStyleAccess(true));
        }
        return s3client;
    }

    public InputStream base64ToInputStream(String base64string) throws IOException {
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] bytes = decoder.decodeBuffer(base64string);
        return new ByteArrayInputStream(bytes);
    }

    /**
     * 上传文件(流格式)
     * 文档地址:http://efs.inf.17usoft.com/docs/s3proxy/s3proxy-usejavasdk
     *
     * @param fileName 文件名称
     * @param input    文件流
     */
    public void putObjByStream(String fileName, InputStream input) {

        AmazonS3Client s3client = getS3client();
        ObjectMetadata meta = new ObjectMetadata();
        try {
            meta.setContentLength(input.available());
        } catch (IOException e) {
            log.error("获取流字节长度异常", e);
        }
        PutObjectRequest request = new PutObjectRequest(BUCKET_NAME, fileName, input, meta);
        s3client.putObject(request);
    }

    /**
     * 获取文件
     *
     * @param fileName 文件名
     * @return 文件对象
     */
    public S3Object getFile(String fileName) {
        AmazonS3Client s3client = getS3client();
        GetObjectRequest request = new GetObjectRequest(BUCKET_NAME, fileName);
        S3Object object = s3client.getObject(request);
        return object;
    }

    /**
     * 根据key产生完整的url
     *
     * @param fileName 文件名
     * @return 强烈建议使用生成公有地址（公有地址的url不变，这样ceph后端可以有缓存作用，也不会穿透CDN），生成公有地址方法点击链接：http://wiki.17usoft.com/pages/viewpage.action?pageId=15040422
     */
    public String getFileUrl(String fileName) {
        AmazonS3Client s3client = getS3client();
        URL url = s3client.getUrl(BUCKET_NAME, fileName);
        String prepareUrl = url.toString();
        return prepareUrl.replace(PRIVATE_URL, PUBLIC_URL);
    }

    /**
     * 根据bucket与key 产生完整的url
     *
     * @param bucket
     * @param key
     * @return 强烈建议使用生成公有地址（公有地址的url不变，这样ceph后端可以有缓存作用，也不会穿透CDN），生成公有地址方法点击链接：http://wiki.17usoft.com/pages/viewpage.action?pageId=15040422
     */
    public String getPrivateURL(String fileName) {
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(BUCKET_NAME, fileName);
        Date expirationDate = null;
        try {
            expirationDate = new SimpleDateFormat("yyyy-MM-dd").parse("2029-12-31");
        } catch (Exception e) {
            e.printStackTrace();
        }
        request.setExpiration(expirationDate);
        URL url = getS3client().generatePresignedUrl(request);
        return url == null ? "none" : url.toString();
    }

    /**
     * 分段上传大文件到S3
     *
     * @param fileName 文件名称
     * @param file     本地文件
     */
    /**
     * 使用 CompletableFuture 优化的分段上传方法，支持外部传入线程池
     *
     * @param fileName 文件名称
     * @param file     本地文件
     * @param executor 外部传入的线程池
     */
    public void multipartUploadAsync(String fileName, File file, ExecutorService executor) {
        AmazonS3Client s3client = getS3client();

        long contentLength = file.length();
        long partSize = 10 * 1024 * 1024; // 每个分片10MB
        String uploadId = null;

        try {
            // 初始化分段上传
            InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(BUCKET_NAME, fileName);
            InitiateMultipartUploadResult initResponse = s3client.initiateMultipartUpload(initRequest);
            uploadId = initResponse.getUploadId();

            // 计算分片总数
            int numberOfParts = (int) Math.ceil((double) contentLength / partSize);

            // 创建并执行所有分片上传任务
            String finalUploadId = uploadId;
            List<CompletableFuture<PartETag>> futures = IntStream.rangeClosed(1, numberOfParts)
                    .mapToObj(partNumber -> uploadPartAsync(s3client, fileName, file, finalUploadId, partNumber, partSize, contentLength, executor))
                    .collect(Collectors.toList());

            // 等待所有分片上传完成并收集 PartETag
            CompletableFuture<List<PartETag>> allPartsFuture = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            ).thenApply(v -> futures.stream()
                    .map(CompletableFuture::join)
                    .collect(Collectors.toList()));

            List<PartETag> partETags = allPartsFuture.join();

            // 检查是否所有分片都上传成功
            if (partETags.size() != numberOfParts) {
                throw new RuntimeException("Not all parts were uploaded successfully. Expected: " + numberOfParts + ", Actual: " + partETags.size());
            }

            // 完成分段上传
            CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(
                    BUCKET_NAME, fileName, uploadId, partETags);
            s3client.completeMultipartUpload(compRequest);

            log.info("Async multipart upload completed successfully for file: {}", fileName);

        } catch (Exception e) {
            log.error("Async multipart upload failed for file: {}", fileName, e);
            // 如果上传失败，取消分段上传
            if (uploadId != null) {
                try {
                    AbortMultipartUploadRequest abortRequest = new AbortMultipartUploadRequest(
                            BUCKET_NAME, fileName, uploadId);
                    s3client.abortMultipartUpload(abortRequest);
                } catch (Exception abortException) {
                    log.error("Failed to abort multipart upload for file: {}", fileName, abortException);
                }
            }
            throw new RuntimeException("Async multipart upload failed", e);
        }
    }

    /**
     * 异步上传单个分片
     */
    private CompletableFuture<PartETag> uploadPartAsync(AmazonS3Client s3client,
                                                        String fileName,
                                                        File file,
                                                        String uploadId,
                                                        int partNumber,
                                                        long partSize,
                                                        long contentLength,
                                                        ExecutorService executor) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                long filePosition = (partNumber - 1) * partSize;
                long currentPartSize = Math.min(partSize, contentLength - filePosition);

                UploadPartRequest uploadRequest = new UploadPartRequest()
                        .withBucketName(BUCKET_NAME)
                        .withKey(fileName)
                        .withUploadId(uploadId)
                        .withPartNumber(partNumber)
                        .withFileOffset(filePosition)
                        .withFile(file)
                        .withPartSize(currentPartSize);

                //System.out.println("Uploaded part " + partNumber + " of file: " + fileName + " time=" + new Date().toLocaleString() + " Thread=" + Thread.currentThread().getName());
                UploadPartResult uploadResult = s3client.uploadPart(uploadRequest);
                log.info("Uploaded part {} of file: {}", partNumber, fileName);
                return uploadResult.getPartETag();
            } catch (Exception e) {
                log.error("Failed to upload part {} of file: {}", partNumber, fileName, e);
                throw new RuntimeException("Failed to upload part " + partNumber, e);
            }
        }, executor);
    }


}
