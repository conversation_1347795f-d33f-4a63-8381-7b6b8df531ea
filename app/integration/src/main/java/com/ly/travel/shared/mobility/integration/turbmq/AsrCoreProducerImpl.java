package com.ly.travel.shared.mobility.integration.turbmq;

import com.ly.sof.api.exception.MQException;
import com.ly.sof.api.mq.common.SerializeEnum;
import com.ly.sof.api.mq.common.UniformEvent;
import com.ly.sof.api.mq.producer.DefaultProducer;
import com.ly.sof.api.mq.producer.UniformEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.HashMap;

@Slf4j
public class AsrCoreProducerImpl extends DefaultProducer implements AsrCoreProducer {
    /**
     * 统一消息事件发布器
     */
    private UniformEventPublisher uniformEventPublisher;

    @Override
    public boolean sendDelayMQ(String topic, String eventCode, Object payload, Date delayTime, SerializeEnum serialize) {
        Assert.notNull(StringUtils.isNotBlank(topic), "Topic is required");
        Assert.notNull(StringUtils.isNotBlank(eventCode), "Event code is required");
        Assert.notNull(payload, "Payload cannot be null");
        Assert.notNull(serialize, "SerializeEnum cannot be null");
        UniformEvent event = uniformEventPublisher.createUniformEvent(topic, eventCode, false, payload);
        event.setTimeout(-1);
        event.setDelayTime(delayTime);
        event.setSerialize(serialize);
        event.addProperties(new HashMap<>());
        boolean success = false;
        try {
            success = uniformEventPublisher.publishUniformEvent(event);
        } catch (MQException e) {
            log.error("延迟mq发送失败", e);
        }
        return success;
    }

    @Override
    public boolean sendDelayMQ(String topic, String eventCode, Object payload, Date delayTime) {
        return sendDelayMQ(topic,eventCode,payload,delayTime,SerializeEnum.FASTJSON);
    }

    @Override
    public boolean sendMQ(String topic, String eventCode, Object payload, SerializeEnum serialize) {
        boolean success = false;
        try {
             success = send(topic, eventCode, payload, serialize);
        } catch (MQException e) {
            e.printStackTrace();
            log.error("mq发送失败",e);
        }
        return success;
    }

    @Override
    public boolean sendMQ(String topic, String eventCode, Object payload) {
        return sendMQ(topic,eventCode, payload,SerializeEnum.FASTJSON);
    }

    /**
     * Setter for UniformEventPublisher
     *
     * @param uniformEventPublisher
     */
    @Override
    public void setUniformEventPublisher(UniformEventPublisher uniformEventPublisher) {
        super.setUniformEventPublisher(uniformEventPublisher);
        this.uniformEventPublisher = uniformEventPublisher;
    }

}
