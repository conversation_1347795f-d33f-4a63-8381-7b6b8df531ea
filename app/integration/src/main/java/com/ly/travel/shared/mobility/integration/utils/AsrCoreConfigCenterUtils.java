package com.ly.travel.shared.mobility.integration.utils;

import com.alibaba.fastjson.JSON;
import com.ly.tcbase.config.ConfigCenterClient;
import com.ly.travel.shared.mobility.integration.utils.dto.InspectorAccountDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Slf4j
public class AsrCoreConfigCenterUtils {

    private static String getValue(String key) {
        String val = null;
        try {
            val = ConfigCenterClient.get(key);
        } catch (Exception e) {
            log.error("", e);
        }
        return val;
    }

    private static String getValue(String appUk,String key) {
        String val = null;
        try {
            val = ConfigCenterClient.get(appUk,key);
        } catch (Exception e) {
            log.error("", e);
        }
        return val;
    }

    /**
     * 获取es 日志的配置
     *
     * @param funName 方法名称
     * @return
     */
    public static InspectorAccountDTO getInspectorAccount(String account) {
        String jsonStr = null;
        try {
            List<InspectorAccountDTO> userInfoList = getInspectorAccountList();
            InspectorAccountDTO userInfo = userInfoList.stream().filter(s -> s.getUserAccount().equals(account)).findFirst().orElse(null);
            return userInfo;
        } catch (Exception e) {
            log.error("", e);
            return null;
        }
    }

    public static List<InspectorAccountDTO> getInspectorAccountList() {
        String jsonStr = null;
        try {
            jsonStr = getValue("groundtravel.shared.mobility.asr.admin","allAccount");

            if (StringUtils.isBlank(jsonStr)) {
                return new ArrayList<>();
            }
            List<InspectorAccountDTO> userInfoList = JSON.parseArray(jsonStr, InspectorAccountDTO.class);
            return userInfoList;
        } catch (Exception e) {
            log.error("", e);
        }
        return new ArrayList<>();
    }


}
