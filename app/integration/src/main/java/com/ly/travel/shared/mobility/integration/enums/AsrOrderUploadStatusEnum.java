package com.ly.travel.shared.mobility.integration.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum AsrOrderUploadStatusEnum {
    WAIT_UPLOAD(0,"待上传"),
    HAD_UPLOAD(1,"已上传"),
    NOT_QUALIFIED(2,"不合格"),
    QUALIFICATION(3,"合格"),
    ;

    private final Integer status;

    private final String desc;

    public static String getDescByCode(Integer code){
        AsrOrderUploadStatusEnum typeEnum = Arrays.stream(values()).filter(s -> s.getStatus().equals(code)).findFirst().orElse(null);
        if(typeEnum !=null){
            return typeEnum.desc;
        }
        return StringUtils.EMPTY;
    }
}
