package com.ly.travel.shared.mobility.integration.utils.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LogAspectTempDTO {
    //类名
    private String className;
    //方法名
    private String methodName;
    //请求json
    private String reqJson;
    //开始时间
    private Long startTime;
    //channelCode
    private String channelCode;
}
