package com.ly.travel.shared.mobility.integration.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum AsrCoreErrorEnum {
    SUCCESS(0, "请求成功"),
    TOKEN_FAIL(101, "token校验失败"),
    PARAM_ERROR(900, "参数错误"),
    BUSINESS_ERROR(901, "业务错误"),
    AUTH_ERROR(902, "鉴权失败"),
    SYSTEM_EXCEPTION(999, "系统异常"),
    ;

    private Integer code;
    private String desc;


}
