package com.ly.travel.shared.mobility.web.controller.chunk;

import com.ly.travel.shared.mobility.biz.model.req.chunk.ChunkMergeReq;
import com.ly.travel.shared.mobility.biz.model.req.chunk.ChunkUploadInitReq;
import com.ly.travel.shared.mobility.biz.model.req.chunk.ChunkUploadReq;
import com.ly.travel.shared.mobility.biz.model.resp.AsrCoreBaseResp;
import com.ly.travel.shared.mobility.biz.model.resp.chunk.ChunkMergeResp;
import com.ly.travel.shared.mobility.biz.model.resp.chunk.ChunkUploadInitResp;
import com.ly.travel.shared.mobility.biz.service.ChunkFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 分块上传控制器
 */
@Slf4j
@RestController
@RequestMapping("chunk")
public class ChunkUploadController {
    
    @Resource
    private ChunkFileService chunkFileService;
    
    /**
     * 初始化分块上传
     */
    @PostMapping("init")
    public AsrCoreBaseResp<ChunkUploadInitResp> initChunkUpload(@Valid @RequestBody ChunkUploadInitReq req) {
        log.info("初始化分块上传: fileName={}, fileSize={}", req.getFileName(), req.getFileSize());
        ChunkUploadInitResp resp = chunkFileService.initChunkUpload(req);
        return AsrCoreBaseResp.ok(resp);
    }
    
    /**
     * 上传分块
     */
    @PostMapping("upload")
    public AsrCoreBaseResp<Void> uploadChunk(
            @RequestParam("uploadId") String uploadId,
            @RequestParam("chunkNumber") Integer chunkNumber,
            @RequestParam("chunkMd5") String chunkMd5,
            @RequestParam("file") MultipartFile file) {
        
        log.info("上传分块: uploadId={}, chunkNumber={}", uploadId, chunkNumber);
        
        ChunkUploadReq req = new ChunkUploadReq();
        req.setUploadId(uploadId);
        req.setChunkNumber(chunkNumber);
        req.setChunkMd5(chunkMd5);
        
        chunkFileService.uploadChunk(req, file);
        return AsrCoreBaseResp.ok();
    }
    
    /**
     * 合并分块文件
     */
    @PostMapping("merge")
    public AsrCoreBaseResp<ChunkMergeResp> mergeChunks(@Valid @RequestBody ChunkMergeReq req) {
        log.info("合并分块文件: uploadId={}", req.getUploadId());
        ChunkMergeResp resp = chunkFileService.mergeChunks(req);
        return AsrCoreBaseResp.ok(resp);
    }
    
    /**
     * 获取上传进度
     */
    @GetMapping("progress/{uploadId}")
    public AsrCoreBaseResp<Double> getUploadProgress(@PathVariable String uploadId) {
        double progress = chunkFileService.getUploadProgress(uploadId);
        return AsrCoreBaseResp.ok(progress);
    }
}
