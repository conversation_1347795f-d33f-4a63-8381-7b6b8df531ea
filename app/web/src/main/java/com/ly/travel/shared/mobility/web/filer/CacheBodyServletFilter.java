package com.ly.travel.shared.mobility.web.filer;

import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class CacheBodyServletFilter implements Filter {


    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws ServletException, IOException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse = (HttpServletResponse) response;

        // 替换请求对象 HttpServletRequest -> CacheBodyRequest
        CacheBodyRequest cacheBodyRequest = new CacheBodyRequest(httpServletRequest);

        chain.doFilter(cacheBodyRequest, httpServletResponse);
    }

    @Override
    public void destroy() {

    }


}
