package com.ly.travel.shared.mobility.web.auth;

import com.ly.travel.shared.mobility.biz.model.resp.AsrCoreBaseResp;
import com.ly.travel.shared.mobility.integration.enums.AsrCoreErrorEnum;
import com.ly.travel.shared.mobility.supply.authentication.model.AuthenticationFailureReasonEnum;
import com.ly.travel.shared.mobility.supply.authentication.model.UserInfo;
import com.ly.travel.shared.mobility.supply.authentication.strategy.AuthenticationFailurePostProcessor;
import org.springframework.stereotype.Component;


@Component("responseAuthFailurePostProcessor")
public class ResponseAuthFailurePostProcessor implements AuthenticationFailurePostProcessor {

    @Override
    public Object buildResultWhenAuthenticationFailure(AuthenticationFailureReasonEnum authenticationFailureReason, UserInfo userInfo) {
        AsrCoreBaseResp resp = new AsrCoreBaseResp();
       resp.setErrCode(AsrCoreErrorEnum.AUTH_ERROR.getCode());
       resp.setMsg(authenticationFailureReason.getErrorMessage());
       return  resp;
    }
}
