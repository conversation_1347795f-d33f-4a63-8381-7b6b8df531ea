package com.ly.travel.shared.mobility.web.aspect;


import com.alibaba.fastjson.JSON;
import com.ly.sof.utils.log.LogContextUtils;
import com.ly.travel.shared.mobility.integration.utils.LogThreadLocalUtils;
import com.ly.travel.shared.mobility.integration.utils.dto.LogAspectTempDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.ValidationException;
import javax.validation.Validator;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 请求记录
 */
@Aspect
@Component
@Slf4j
@Order(-10)
public class AsrCoreRequestAspect {
    private static final Validator VALIDATOR = Validation.buildDefaultValidatorFactory().getValidator();


    @Pointcut(
            " execution(* com.ly.travel.shared.mobility.web.controller..*.*(..))"
    )
    private void apiRequest() {

    }

    /**
     * 健康检查的地址排除
     */
    @Pointcut(
            " execution(* com.ly.travel.shared.mobility.web.controller.IndexController.*(..))"
    )
    private void indexRequest() {

    }



    @Pointcut("apiRequest() && !indexRequest()")
    private void allRequest() {

    }

    @Around("allRequest()")
    public Object doAround(ProceedingJoinPoint call) throws Throwable {
        Long startTime = System.currentTimeMillis();

        Object result = null;
        String methodName = call.getSignature().getName();
        String className = call.getTarget().getClass().getSimpleName();

        Object[] args = call.getArgs();
        //获取处理后的请求参数json
        String reqJson = getReqJson();
        LogContextUtils.setMarker(className, methodName);
        log.info("==> request method:{}.{}, params: {}", className, methodName, reqJson);
        LogAspectTempDTO saveDTO = LogAspectTempDTO.builder().className(className).methodName(methodName).reqJson(reqJson).startTime(startTime).build();
        LogThreadLocalUtils.getInstance().setValue(saveDTO);
        validateArguments(args);
        result = call.proceed();
        //记录请求日志
        return result;
    }


    private Map<String, Object> getMap() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();

        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, Object> map = new HashMap<>();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            Object val = null;
            if (ArrayUtils.isNotEmpty(entry.getValue())) {
                val = entry.getValue()[0];
            }
            map.put(entry.getKey(), val);
        }
        return map;
    }

    /**
     * 获取请求参数的json 数据
     *
     * @param
     * @return
     */
    private String getReqJson() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes.getRequest();
            String bodyStr = IOUtils.toString(request.getInputStream(), StandardCharsets.UTF_8);

            if (StringUtils.isNotBlank(bodyStr)) {
                return bodyStr;
            }
            //没有的话从参数中获取
            Map<String, Object> map = getMap();
            return JSON.toJSONString(map);
        } catch (Exception e) {
            log.error("getReqJson 异常", e);
        }
        return StringUtils.EMPTY;
    }

    @AfterReturning(
            pointcut = "allRequest() || execution(public * com.ly.travel.shared.mobility.web.advice.*Advice.*(..))",
            returning = "result"
    )
    public void afterReturning(Object result) {
        try {
            String respJson = JSON.toJSONString(result);
            LogAspectTempDTO logAspectTempDTO = LogThreadLocalUtils.getInstance().getValue();
            if (logAspectTempDTO == null) {
                log.warn("未获取到logAspectTempDTO信息");
                return;
            }
            Long startTime = logAspectTempDTO.getStartTime();
            Long cost = System.currentTimeMillis() - startTime;
            log.info("==> response method spend：{},result: {}", cost, respJson);
        } catch (Exception e) {
            log.error("afterReturningAdvice 异常", e);
        } finally {
            LogContextUtils.removeAll();
            LogThreadLocalUtils.getInstance().removeValue();
        }
    }


    public void validateArguments(Object[] args) {
        // 无参方法直接return
        if (ArrayUtils.isEmpty(args)) {
            return;
        }

        for (Object obj : args) {
            validateParamObject(obj);
        }
    }

    private void validateParamObject(Object obj) {
        Set<ConstraintViolation<Object>> violations = VALIDATOR.validate(obj);
        if (violations.isEmpty()) {
            return;
        }
        for (ConstraintViolation<Object> constraintViolation : violations) {
            throw new ValidationException(constraintViolation.getPropertyPath() + constraintViolation.getMessage());
        }
    }
}
