
package com.ly.travel.shared.mobility.web.advice;


import com.ly.travel.shared.mobility.biz.model.resp.AsrCoreBaseResp;
import com.ly.travel.shared.mobility.integration.enums.AsrCoreErrorEnum;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreException;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreWarnException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ValidationException;


@Slf4j
@RestControllerAdvice(basePackages = "com.ly.travel.shared.mobility.web.controller")
public class AsrCoreExceptionAdvice {
    private static final String LOG_PRINT_TEMPLATE = "AsrCoreExceptionAdvice:{}";

    /**
     * 捕获 {@code AsrCoreException} 异常
     */
    @ExceptionHandler({AsrCoreException.class})
    public AsrCoreBaseResp<?> handleAsrCoreException(AsrCoreException ex) {
        log.error(LOG_PRINT_TEMPLATE, ex.getMessage());
        return AsrCoreBaseResp.fail(ex.getCode(), ex.getMessage());
    }



    @ExceptionHandler({ValidationException.class})
    public AsrCoreBaseResp<?> handleValidationException(ValidationException ex) {
        log.error(LOG_PRINT_TEMPLATE, ex.getMessage());
        return AsrCoreBaseResp.fail(AsrCoreErrorEnum.PARAM_ERROR.getCode(),ex.getMessage());
    }

    /**
     * 警告异常异常
     */
    @ExceptionHandler({AsrCoreWarnException.class})
    public AsrCoreBaseResp<?> handle(AsrCoreWarnException ex) {
        log.warn(LOG_PRINT_TEMPLATE, ex.getMessage());
        return AsrCoreBaseResp.fail(ex.getCode(), ex.getMessage());
    }



    /**
     * 顶级异常捕获并统一处理，当其他异常无法处理时候选择使用
     */
    @ExceptionHandler({Exception.class})
    public AsrCoreBaseResp<?> handle(Exception ex) {
        ex.printStackTrace();
        Throwable cause = ex.getCause();
        if (cause != null) {
            log.error(LOG_PRINT_TEMPLATE, cause);
            return AsrCoreBaseResp.fail(cause.getMessage());
        }
        String message = ex.getMessage();
        if(message != null) {
            log.error(LOG_PRINT_TEMPLATE, ex);
            return AsrCoreBaseResp.fail(message);
        }
        log.error(LOG_PRINT_TEMPLATE, ex);
        return AsrCoreBaseResp.fail(ex.toString());
    }


}
