package com.ly.travel.shared.mobility.web.controller;

import com.ly.travel.shared.mobility.biz.model.req.order.OrderQueryReq;
import com.ly.travel.shared.mobility.biz.service.AsyncRecognitionService;
import com.ly.travel.shared.mobility.biz.service.OrderService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * @Description:
 * @Author: jay.he
 * @Date: 2025-08-19 17:14
 * @Version: 1.0
 **/
@RestController
@RequestMapping("/recognition")
public class TestController {

    @Resource
    private AsyncRecognitionService asyncRecognitionService;

    @Resource
    private OrderService orderService;

    @PostMapping("/uploadCompress")
    public ResponseEntity<?> uploadCompressFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam("taskId") String taskId) throws IOException {
        return ResponseEntity.ok(asyncRecognitionService.unzipAndAsyncRecognize(taskId, file));
    }

    @GetMapping("/getList")
    public ResponseEntity<?> getList(String phoneNo) throws IOException {
        return ResponseEntity.ok(orderService.querySfcOrderList(OrderQueryReq.builder().phoneNo(phoneNo).startTime("2025-01-01").endTime("2025-08-19").build()));
    }

}
