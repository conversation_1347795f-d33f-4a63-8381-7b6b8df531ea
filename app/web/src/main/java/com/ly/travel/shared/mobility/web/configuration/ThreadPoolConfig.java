package com.ly.travel.shared.mobility.web.configuration;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Description: 线程池配置
 * @Author: jay.he
 * @Date: 2025-08-19 17:48
 * @Version: 1.0
 **/
@Configuration
public class ThreadPoolConfig {


    /**
     * 语音识别线程池
     */
    @Bean(name = "recognitionExecutorService")
    public ThreadPoolTaskExecutor recognitionExecutorService() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(32);
        // 最大线程数
        executor.setMaxPoolSize(64);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程空闲时间
        executor.setKeepAliveSeconds(300);
        // 线程名称前缀
        executor.setThreadNamePrefix("recognition-task-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();
        return executor;
    }


    /**
     * 分块上传S3线程池
     *
     * @return
     */
    @Bean(name = "uploadS3ExecutorService")
    public ExecutorService uploadS3ExecutorService() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程数
        executor.setCorePoolSize(32);
        // 最大线程数
        executor.setMaxPoolSize(64);
        // 队列容量
        executor.setQueueCapacity(100);
        // 线程空闲时间
        executor.setKeepAliveSeconds(300);
        // 线程名称前缀
        executor.setThreadNamePrefix("upload-s3-task-");
        // 拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();
        return executor.getThreadPoolExecutor();
    }
}
