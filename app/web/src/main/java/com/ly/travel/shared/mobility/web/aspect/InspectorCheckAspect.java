package com.ly.travel.shared.mobility.web.aspect;


import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorBaseReq;
import com.ly.travel.shared.mobility.biz.service.InspectorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 请求记录
 */
@Aspect
@Component
@Slf4j
@Order(1)
public class InspectorCheckAspect {
    @Resource
    private InspectorService inspectorService;


    @Pointcut(
            " execution(* com.ly.travel.shared.mobility.web.controller.inspector..*.*(..))"
    )
    private void apiRequest() {

    }


    @Pointcut("apiRequest()")
    private void allRequest() {

    }

    @Around("allRequest()")
    public Object doAround(ProceedingJoinPoint call) throws Throwable {
        Object result = null;
        Object[] args = call.getArgs();
        //检查员登录校验
        checkInspector(args);
        //获取处理后的请求参数json
        result = call.proceed();
        //记录请求日志
        return result;
    }

    private void checkInspector(Object[] args) {
        if (ArrayUtils.isEmpty(args)) {
            return;
        }
        for (Object arg : args) {
            if (!(arg instanceof InspectorBaseReq)) {
                continue;
            }
            InspectorBaseReq baseReq = (InspectorBaseReq) arg;
            inspectorService.checkInspector(baseReq);
        }

    }

}
