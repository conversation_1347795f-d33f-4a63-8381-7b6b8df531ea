import axios from 'axios'
import router from '@/router'
import { Message } from 'element-ui'

axios.defaults.withCredentials = true

const http = {
    install(Vue) {
        Vue.prototype.$http = config => {
            const promise = new Promise((resolve, reject) => {
                if (!config.data) {
                    config.data = {}
                }
                config.url = '/carAsrCore/' + config.url

                if (/get/i.test(config.method) && config.data) {
                    config.params = {
                        ...config.data,
                    }
                }
                axios(config).then(result => {
                    if (result.data) {
                        if(result.data.errCode === 101){
                            Message.error('登录过期,请重新登录')
                            router.push('/login')
                            return
                        }
                        resolve(result.data)
                    } else {
                        reject()
                    }
                }).catch(e => {
                    console.warn(e)
                    reject(e)
                })
            })

            return promise
        }
    }
}

export default http
