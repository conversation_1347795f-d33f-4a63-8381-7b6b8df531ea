// 数组校验
export const validateList = (rule, value, callback) => {
    if (value.length < 1) {
        callback(new Error("至少添加一条数据"));
    } else {
        callback();
    }
};

export const isPisitiveInteger = (value) =>{
    return /^[1-9]\d*$/.test(value)
}

// 最大4位
export const isMaxLength4Integer = (value) =>{
    return /^\d{4}$/.test(value)
}

export const validateNum = (rule, value, callback) =>{
    if (value == 0) {
        callback(new Error('最小数值为1'));
    }
    else if (!value) {
        callback(new Error('请输入最小数值'));
    }
    else if (!isPisitiveInteger(value)) {
        callback(new Error('请输入数值'));
    }else{
        callback();
    }
}

const judgeAuthority = (buttonList, name) => {
    if (buttonList.length) {
        for (let i = 0, len = buttonList.length; i < len; i++) {
            if (buttonList[i] === name) {
                return true
            }
        }
    }

    return false
}

const formatParams = data => {
    const arr = []

    for (const name in data) {
        arr.push(encodeURIComponent(name) + '=' + encodeURIComponent(data[name]))
    }

    return arr.join('&')
}

const isString = obj => Object.prototype.toString.call(obj) === '[object String]'

const isEmpty = obj => {
    if (obj == null) return true
    if (Array.isArray(obj) || isString(obj)) return obj.length === 0
    for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) return false
    }
    return true
}

const getSession = name => {
    const val = window.sessionStorage.getItem(name)

    try {
        return JSON.parse(val)
    } catch (e) {
        return val || null
    }
}

const setSession = (name, content) => {
    if (!isString(content)) {
        content = JSON.stringify(content)
    }
    window.sessionStorage.setItem(name, content)
}

export {
    judgeAuthority,
    isEmpty,
    formatParams,
    setSession,
    getSession,
}
