import { formatDate, deepCopy, formatParams } from './index'

const vueTools = {
    install(Vue) {
        Vue.prototype.formatDate = (timeSpan, format = 'yyyy-MM-dd hh:mm:ss') => {
            let date = new Date(timeSpan)
            return formatDate(date, format)
        }
        Vue.prototype.deepCopy = obj => {
            return deepCopy(obj)
        }
        Vue.prototype.formatParams = obj => {
            return formatParams(obj)
        }

        Vue.prototype.simpleConfirm = async(msg) => {
            let confirm = false
            await Vue.prototype.$confirm(msg, '警告', {
                closeOnClickModal: false,
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                confirm = true
            }).catch(() => {
            })
            return confirm
        }
    }
}

export default vueTools
