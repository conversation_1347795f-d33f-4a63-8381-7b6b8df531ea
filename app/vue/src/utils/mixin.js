let mixIn = {
    data() {
        return {}
    },
    methods: {
        openItem(endPath, name) {
            let tarUrl = window.location.origin + window.location.pathname + endPath
            try {
                window.parent.addItem(tarUrl, name)
            } catch (e) {
                window.open(tarUrl);
            }
        },
        logClick(id) {
            this.$refs.operatorLog.showLog(id)
        },
        // input为数字类型，输入框不允许输入'e,E'和'+,-'
        clearScientificInput(e) {
            let key = e.key;
            if (key === 'e' || key === 'E' || key === '+' || key === '-') {
                e.returnValue = false;
                return false;
            }
            return true;
        }
    }
}

export default mixIn
