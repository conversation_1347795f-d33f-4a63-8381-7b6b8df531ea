<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2 class="login-title">请完善您的信息</h2>
      <el-form :model="formData" ref="form" label-width="20">
        <el-form-item label="收件人姓名" prop="mailName" :rules="[{ required: true, message: '收件人姓名不能为空' }]">
          <el-input v-model="formData.mailName" maxlength="200" placeholder="请输入收件人姓名"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="mailPhone" :rules="[{ required: true, message: '手机号不能为空' }]">
          <el-input v-model="formData.mailPhone" maxlength="200" placeholder="请输入收件人手机号 请注意和账号关联的手机号保持一致"></el-input>
        </el-form-item>
        <el-form-item label="邮寄地址" prop="postAddress" :rules="[{ required: true, message: '邮寄地址不能为空' }]">
          <el-input v-model="formData.postAddress" maxlength="200" placeholder="请输入邮寄地址"></el-input>
        </el-form-item>
        <el-button type="primary" @click="submitClick" :loading="loading">提交</el-button>
      </el-form>
    </el-card>
  </div>
</template>


<script>

let orgFormData = {
  postAddress: '',
  mailName: '',
  mailPhone: '',
}


export default {
  components: {},
  data() {
    return {
      formData: this.deepCopy(orgFormData),
      loading: false
    }
  },

  created() {
  },
  mounted() {

  },
  filters: {},
  methods: {
    paymentMethodChange(){
      this.$refs.form.clearValidate('paymentCardNo')
      this.$refs.form.clearValidate('paymentName')
    },
    submitClick() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true
          let param = {
            url: 'inspector/improveInfo',
            method: 'POST',
            data: this.formData
          }
          this.$http(param).then((res) => {
            if (res.success) {
              this.$message.success('信息完善成功')
              this.$router.push('/asrOrderList')

            } else {
              this.$message.error(res.msg)
            }
            this.loading = false

          }).catch(err => {
            console.log(err);
            this.$message.error('提交失败,请稍后重试')
            this.loading = false
          })
        }
      });

    },
  }
};
</script>

<style>
</style>
