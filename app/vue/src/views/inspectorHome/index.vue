<template>
  <div></div>
</template>


<script>
import Cookies from 'js-cookie'


export default {
  components: {},
  data() {
    return {}
  },

  created() {
    this.jumpPage()
  },
  mounted() {

  },
  filters: {},
  methods: {
    jumpPage() {

      let param = {
        url: 'inspector/queryJumpPage',
        method: 'POST',
      }

      this.$http(param).then((res) => {
        if (res.success) {
          if (res.data.jumpPage === 2) {
            this.$router.push('/improveInfo')
          } else if (res.data.jumpPage === 3) {
            this.$router.push('/asrOrderList')
          } else {
            this.$router.push('/login')
          }
        } else {
          this.$router.push('/login')
        }

      }).catch(err => {
        console.log(err);
        this.$router.push('/login')
      })

    },
  }
};
</script>

<style>
</style>
