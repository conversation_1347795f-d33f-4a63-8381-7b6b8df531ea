<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title><%= webpackConfig.name %></title>
  </head>
  <body>
    <div id="app"></div>
    <script type="text/javascript">
        window._AMapSecurityConfig = {
            // serviceHost:'您的代理服务器域名或地址/_AMapService',
            // 开发时先这么用
            securityJsCode: '257071383c65a71f2180a0d6f0931ab7'
        }
    </script>
    <!-- built files will be auto injected -->
  </body>
</html>
