<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ly.travel.shared.mobility</groupId>
        <artifactId>shared-mobility-asr-core-parent</artifactId>
        <version>*******</version>
        <relativePath>../../pom.xml</relativePath>
    </parent>

    <artifactId>shared-mobility-asr-core-test</artifactId>
    <packaging>jar</packaging>
    <name>LY shared-mobility-asr-core-test</name>
    <description>LY shared-mobility-asr-core-test</description>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.ly.flight.toolkit</groupId>
            <artifactId>sof-dev-launcher</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.shared.mobility</groupId>
            <artifactId>shared-mobility-asr-core-biz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ly.travel.shared.mobility</groupId>
            <artifactId>shared-mobility-asr-core-web</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ibatis</groupId>
            <artifactId>ibatis</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!--region Test dependecies -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.sof</groupId>
            <artifactId>sof-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ly.flight.intl</groupId>
            <artifactId>spring-test-plus</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ly.flight.toolkit</groupId>
            <artifactId>spring-test-migration</artifactId>
            <scope>test</scope>
        </dependency>
        <!--endregion -->
    </dependencies>
</project>
