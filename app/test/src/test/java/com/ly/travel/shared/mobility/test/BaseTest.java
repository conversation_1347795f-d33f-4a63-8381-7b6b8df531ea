package com.ly.travel.shared.mobility.test;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.TestPropertySource;

import com.ly.flight.intl.sof.spring.test.RemoteConfig;
import com.ly.flight.intl.sof.spring.test.ReplaceValue;
import com.ly.sof.test.junit5.Spring4junit5Extension;

/**
 * <p>集成测试基类，载入所有spring配置。</p>
 * <p>在集成测试时存在一些场景，比如不想使用真实数据库，不注册dsf等，这些都需要自定义spring配置。</p>
 */
@ExtendWith(Spring4junit5Extension.class)
// 会合并filter内容到conf中，进而不再需要手动拷贝配置文件，如果启用了远程配置，可以将localMode删除或者设为false。
@RemoteConfig(code = "application.code", env = "dev", localMode = true)
@ContextConfiguration(locations = { "classpath*:META-INF/spring/*.xml" })
@TestPropertySource(properties = { "jdbc.username=value2" })
public class BaseTest {
    // 在拉取配置后立刻进行配置替换，先与TestPropertySource, 所以TestPropertySource会覆盖远程配置。
    @ReplaceValue(fileName = "log4j2.xml", key = "log.root")
    private String logRoot = BaseTest.class.getClassLoader().getResource("").getPath();
    // 使用 mockSofContext 替换默认的sofContext，可以定制sofContext，比如不注册mq 等，@see com.ly.sof.test.MockSofContext。
    @ReplaceValue(fileName = "dubbo.properties", key = "sof.context.name")
    private String sofContextName = "com.ly.sof.test.MockSofContext";

    @BeforeAll
    public static void setup() {
    }
}
