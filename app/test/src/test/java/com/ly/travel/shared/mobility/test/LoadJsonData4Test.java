package com.ly.travel.shared.mobility.test;

import java.io.File;
import java.io.IOException;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ResourceUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.ly.sof.utils.mapping.FastJsonUtils;

/**
 * <p>在单元测试或集成测试时，都需要进行数据mock，有些模型巨大，直接写在代码中不方便维护，所以将数据写在json文件中，通过此类读取json文件。</p>
 * <p>json文件的路径是相对于test/resources/json目录的相对路径，如：/order/Order.json</p>
 * <p>使用时，path路径只需要传/json后的相对路径即可。</p>
 */
public class LoadJsonData4Test {

    /**
     * Gets data by path.
     *
     * @param <T>   the type parameter
     * @param path  the path
     * @param clazz the clazz
     * @return the data by path
     */
    public static <T> T getDataByPath(String path, Class<T> clazz) {
        String json = getJsonString(path);
        return FastJsonUtils.fromJSONString(json, clazz);
    }

    /**
     * Gets data by path.
     *
     * @param <T>   the type parameter
     * @param path  the path
     * @param d the clazz
     * @return the data by path
     */
    public static <T> T getDataByPath(String path, TypeReference<T> d) {
        String json = getJsonString(path);
        return FastJsonUtils.fromJSONString(json, d);
    }

    /**
     * Gets list data by path.
     *
     * @param <T>   the type parameter
     * @param path  the path
     * @param clazz the clazz
     * @return the list data by path
     */
    public static <T> List<T> getListDataByPath(String path, Class<T> clazz) {
        String json = getJsonString(path);
        return JSONArray.parseArray(json, clazz);
    }

    /**
     * 读取json字符串
     *
     * @param path
     * @return
     */
    public static String getJsonString(String path) {
        if (StringUtils.isBlank(path)) {
            return null;
        }

        try {
            String bizPath = System.getProperty("user.dir");
            String dir = bizPath + File.separator + "src" + File.separator + "test" + File.separator + "resources" + File.separator + "json";
            if (!path.startsWith("/")) {
                dir = File.separator + dir;
            }
            File file = ResourceUtils.getFile(dir + path);
            return FileUtils.readFileToString(file);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return "";
    }

}
