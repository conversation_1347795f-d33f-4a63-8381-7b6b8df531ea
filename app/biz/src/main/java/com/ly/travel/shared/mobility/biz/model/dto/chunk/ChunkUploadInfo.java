package com.ly.travel.shared.mobility.biz.model.dto.chunk;

import lombok.Data;

import java.util.Date;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分块上传信息
 */
@Data
public class ChunkUploadInfo {
    
    /**
     * 上传任务ID
     */
    private String uploadId;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件大小
     */
    private Long fileSize;
    
    /**
     * 文件MD5
     */
    private String fileMd5;
    
    /**
     * 分块大小
     */
    private Long chunkSize;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 已上传的分块集合（线程安全）
     */
    private Set<Integer> uploadedChunks = ConcurrentHashMap.newKeySet();
    
    /**
     * 上传目录
     */
    private String uploadDir;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 业务标识
     */
    private String businessId;
    
    /**
     * 检查是否所有分块都已上传
     */
    public boolean isAllChunksUploaded() {
        return uploadedChunks.size() == totalChunks;
    }
}
