package com.ly.travel.shared.mobility.biz.model.vo.meta;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Description: 上传文件解析结果
 * @Author: jay.he
 * @Date: 2025-08-19 20:16
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadFileInfoVO {

    /**
     * 是否需要删除  -- 上传S3是否需要删除本地元文件(压缩包需要删除：true，单个文件会在识别后自动删除:false)
     */
    private boolean needDel;

    /**
     * 文件元信息列表(压缩包：包含所有解压后文件信息， 单个文件：包含自身文件信息)
     */
    private List<FileMetaInfo> fileMetaInfoList;

}
