package com.ly.travel.shared.mobility.biz.service;

import com.ly.sof.utils.common.DateUtil;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderDetailRequest;
import com.ly.travel.car.orderservice.facade.request.ivr.IVROrderListRequest;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderDetailResponse;
import com.ly.travel.car.orderservice.facade.response.ivr.IVROrderListResponse;
import com.ly.travel.shared.mobility.biz.model.dto.order.OrderInfoDTO;
import com.ly.travel.shared.mobility.biz.model.req.order.OrderQueryReq;
import com.ly.travel.shared.mobility.integration.client.orderservice.OrderServiceClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Description: 订单查询
 * @Author: jay.he
 * @Date: 2025-08-19 11:39
 * @Version: 1.0
 **/
@Slf4j
@Service
public class OrderService  {

    @Resource
    private OrderServiceClient ivrOrderClient;

    public List<OrderInfoDTO> querySfcOrderList(OrderQueryReq req) {
        IVROrderListRequest listReq = new IVROrderListRequest();
        listReq.setOrderStatus("300");
        listReq.setMobilePhoneNo(req.getPhoneNo());
        listReq.setOrderStartDate(req.getStartTime());
        listReq.setOrderEndDate(req.getEndTime());
        IVROrderListResponse listRes = ivrOrderClient.queryIvrOrderList(listReq);
        return CollectionUtils.emptyIfNull(listRes.getData())
                .stream()
                .map(order -> {
                    IVROrderDetailRequest request = new IVROrderDetailRequest();
                    request.setOrderId(order.getOrderId());
                    request.setTraceId(req.getTraceId());
                    return ivrOrderClient.orderDetail(request).getData();
                })
                .map(order ->
                        OrderInfoDTO
                                .builder()
                                .orderNo(order.getOrderHeadInfo().getOrderShowId())
                                .passengerOnCarTime(Optional.ofNullable(order.getOrderBookingInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderBookingInfo::getPassengerOnCarTime)
                                        .map(DateUtil::getNewFormatDateString)
                                        .orElse(null))
                                .passengerArriveTime(Optional.ofNullable(order.getOrderBookingInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderBookingInfo::getPassengerArriveTime)
                                        .map(DateUtil::getNewFormatDateString)
                                        .orElse(null))
                                .carNo(Optional.ofNullable(order.getOrderCarInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderCarInfo::getPlateNumber)
                                        .orElse(null))
                                .driverName(Optional.ofNullable(order.getOrderCarInfo())
                                        .map(IVROrderDetailResponse.IVROrderDetail.IVROrderCarInfo::getDriverName)
                                        .orElse(null))
                                .build())
                .collect(Collectors.toList());
    }
}
