package com.ly.travel.shared.mobility.biz.service;

import com.ly.travel.shared.mobility.biz.model.vo.meta.FileMetaInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * @Description: 异步识别
 * @Author: jay.he
 * @Date: 2025-08-18 17:26
 * @Version: 1.0
 **/
public interface AsyncRecognitionService {

    /**
     * 原文件上传S3、解压获取音视频文件、并异步识别
     *
     * @param taskId
     * @param file
     * @return
     */
    public String unzipAndAsyncRecognize(String taskId, MultipartFile file) throws IOException;

    /**
     * 异步识别
     *
     * @param taskId
     * @param fileMetadataList
     */
    public void recognize(String taskId, List<FileMetaInfo> fileMetadataList);


    /**
     * 异步识别
     *
     * @param taskId
     * @param fileMetaInfo
     */
    public void recognize(String taskId, FileMetaInfo fileMetaInfo);

}
