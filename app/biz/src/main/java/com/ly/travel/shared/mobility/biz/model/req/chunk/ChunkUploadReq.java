package com.ly.travel.shared.mobility.biz.model.req.chunk;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分块上传请求
 */
@Data
public class ChunkUploadReq {
    
    /**
     * 上传任务ID
     */
    @NotBlank(message = "上传任务ID不能为空")
    private String uploadId;
    
    /**
     * 分块序号（从1开始）
     */
    @NotNull(message = "分块序号不能为空")
    private Integer chunkNumber;
    
    /**
     * 分块MD5值
     */
    @NotBlank(message = "分块MD5不能为空")
    private String chunkMd5;
}
