package com.ly.travel.shared.mobility.biz.model.vo.meta;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @Description: 上传文件元信息
 * @Author: jay.he
 * @Date: 2025-08-18 15:57
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileMetaInfo {
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件大小（字符串格式）
     */
    private String fileSizeStr;

    /**
     * 文件创建时间
     */
    private Date createTime;

    /**
     * 文件修改时间
     */
    private Date modifyTime;

    /**
     * 文件扩展名
     */
    private String extension;

    /**
     * 文件是否可读
     */
    private Boolean isReadable;

    /**
     * 文件序号
     * 按时间顺序排序，时间越久值越小
     */
    private int seqNo;
}
