package com.ly.travel.shared.mobility.biz.service;

import com.ly.spat.dsf.utils.StringUtils;
import com.ly.travel.shared.mobility.biz.model.resp.upload.UploadResp;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreException;
import com.ly.travel.shared.mobility.integration.utils.S3ClientApi;
import com.ly.travel.shared.mobility.integration.utils.SerialNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

@Slf4j
@Service
public class UploadService {
    @Resource
    private S3ClientApi s3ClientApi;

    public UploadResp upload(MultipartFile file) {
        if (StringUtils.isEmpty(file.getOriginalFilename())) {
            throw new AsrCoreException("文件名为空");
        }

        String originalFilename = generateOriginalFileName(file);
        try {
            s3ClientApi.putObjByStream(originalFilename, file.getInputStream());
        } catch (IOException e) {
            log.error(e.getMessage());
        }

        String fileUrl = s3ClientApi.getFileUrl(originalFilename);

        UploadResp resp = new UploadResp();
        resp.setFileUrl(fileUrl);
        resp.setFileName(originalFilename);
        return resp;
    }

    public String generateOriginalFileName(MultipartFile file) {
        return SerialNoUtil.genTraceId().concat("/").concat(Objects.requireNonNull(file.getOriginalFilename()));
    }
}
