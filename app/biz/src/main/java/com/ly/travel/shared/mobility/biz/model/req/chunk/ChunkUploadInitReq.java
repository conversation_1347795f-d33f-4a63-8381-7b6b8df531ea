package com.ly.travel.shared.mobility.biz.model.req.chunk;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分块上传初始化请求
 */
@Data
public class ChunkUploadInitReq {
    
    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String fileName;
    
    /**
     * 文件大小（字节）
     */
    @NotNull(message = "文件大小不能为空")
    private Long fileSize;
    
    /**
     * 文件MD5值（用于校验）
     */
    @NotBlank(message = "文件MD5不能为空")
    private String fileMd5;
    
    /**
     * 分块大小（字节，默认5MB）
     */
    private Long chunkSize = 5 * 1024 * 1024L;
    
    /**
     * 业务标识（如订单号）
     */
    private String businessId;
}
