package com.ly.travel.shared.mobility.biz.model.resp.chunk;

import lombok.Data;

/**
 * 分块上传初始化响应
 */
@Data
public class ChunkUploadInitResp {
    
    /**
     * 上传任务ID
     */
    private String uploadId;
    
    /**
     * 总分块数
     */
    private Integer totalChunks;
    
    /**
     * 分块大小
     */
    private Long chunkSize;
    
    /**
     * 已存在的分块列表（用于断点续传）
     */
    private java.util.List<Integer> existingChunks;
}
