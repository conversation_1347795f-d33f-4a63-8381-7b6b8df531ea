package com.ly.travel.shared.mobility.biz.model.vo.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Description:
 * @Author: jay.he
 * @Date: 2025-08-19 11:38
 * @Version: 1.0
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderQueryVO {

    /**
     * 下单人电话
     */
    private String phoneNo;

    /**
     * 订单查询开始时间: yyyy-MM-dd HH:mm:ss
     */
    private String startTime;

    /**
     * 订单查询结束时间: yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * traceId
     */
    private String traceId;
}
