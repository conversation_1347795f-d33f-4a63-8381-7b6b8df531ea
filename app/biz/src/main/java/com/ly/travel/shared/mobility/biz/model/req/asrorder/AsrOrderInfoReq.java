package com.ly.travel.shared.mobility.biz.model.req.asrorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.travel.shared.mobility.biz.model.req.inspector.InspectorBaseReq;
import lombok.Data;

import java.util.Date;

@Data
public class AsrOrderInfoReq extends InspectorBaseReq {
    /**
     * 订单流水号
     */
    private String orderSerialNo;

    /**
     * 行程开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tripStartTime;

    /**
     * 行程结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tripEndTime;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 上传状态
     */
    private Integer uploadStatus;


    /**
     * 审核状态
     */
    private Integer auditStatus;
}
