package com.ly.travel.shared.mobility.biz.model.resp;

import com.ly.travel.shared.mobility.integration.enums.AsrCoreErrorEnum;
import lombok.Data;

@Data
public class AsrCoreBaseResp<T> {
    /**
     * 错误吗
     */
    private int errCode;
    /**
     * 消息
     */
    private String msg;
    /*

    数据
     */
    private T data;

    public static <T> AsrCoreBaseResp ok(T data) {
        AsrCoreBaseResp resp = new AsrCoreBaseResp();
        resp.setData(data);
        resp.setErrCode(AsrCoreErrorEnum.SUCCESS.getCode());
        resp.setMsg(AsrCoreErrorEnum.SUCCESS.getDesc());
        return resp;
    }

    public static <T> AsrCoreBaseResp ok() {
        AsrCoreBaseResp resp = new AsrCoreBaseResp();
        resp.setErrCode(AsrCoreErrorEnum.SUCCESS.getCode());
        resp.setMsg(AsrCoreErrorEnum.SUCCESS.getDesc());
        return resp;
    }

    public static <T> AsrCoreBaseResp fail(int code, String msg) {
        AsrCoreBaseResp resp = new AsrCoreBaseResp();
        resp.setErrCode(code);
        resp.setMsg(msg);
        return resp;
    }

    public static <T> AsrCoreBaseResp fail(AsrCoreErrorEnum errorEnum) {
        AsrCoreBaseResp resp = new AsrCoreBaseResp();
        resp.setErrCode(errorEnum.getCode());
        resp.setMsg(errorEnum.getDesc());
        return resp;
    }

    public static <T> AsrCoreBaseResp fail(String message) {
        AsrCoreBaseResp resp = new AsrCoreBaseResp();
        resp.setErrCode(AsrCoreErrorEnum.SYSTEM_EXCEPTION.getCode());
        resp.setMsg(message);
        return resp;
    }

    public boolean isSuccess() {
        return AsrCoreErrorEnum.SUCCESS.getCode().equals(this.errCode);
    }

}
