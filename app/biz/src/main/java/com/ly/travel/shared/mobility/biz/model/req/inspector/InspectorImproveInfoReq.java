package com.ly.travel.shared.mobility.biz.model.req.inspector;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class InspectorImproveInfoReq extends InspectorBaseReq {
    /**
     * 邮寄地址
     */
    @NotBlank(message = "邮寄地址不能为空")
    private String postAddress;

    /**
     * 收件人姓名
     */
    @NotNull(message = "收件人姓名不能为空")
    private String mailName;

    /**
     * 收件人手机号
     */
    @NotBlank(message = "收件人手机号不能为空")
    private String mailPhone;
}
