package com.ly.travel.shared.mobility.biz.service;

import com.ly.travel.shared.mobility.biz.model.dto.chunk.ChunkUploadInfo;
import com.ly.travel.shared.mobility.biz.model.req.chunk.ChunkMergeReq;
import com.ly.travel.shared.mobility.biz.model.req.chunk.ChunkUploadInitReq;
import com.ly.travel.shared.mobility.biz.model.req.chunk.ChunkUploadReq;
import com.ly.travel.shared.mobility.biz.model.resp.chunk.ChunkMergeResp;
import com.ly.travel.shared.mobility.biz.model.resp.chunk.ChunkUploadInitResp;
import com.ly.travel.shared.mobility.integration.exception.AsrCoreException;
import com.ly.travel.shared.mobility.integration.utils.SerialNoUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 分块文件管理服务
 */
@Slf4j
@Service
public class ChunkFileService {
    
    /**
     * 存储上传任务信息的内存缓存
     */
    private final ConcurrentHashMap<String, ChunkUploadInfo> uploadInfoCache = new ConcurrentHashMap<>();
    
    /**
     * 文件上传根目录
     */
    @Value("${file.upload.path:/tmp/uploads}")
    private String uploadBasePath;
    
    /**
     * 初始化分块上传
     */
    public ChunkUploadInitResp initChunkUpload(ChunkUploadInitReq req) {
        // 生成上传任务ID
        String uploadId = SerialNoUtil.genTraceId();
        
        // 计算总分块数
        int totalChunks = (int) Math.ceil((double) req.getFileSize() / req.getChunkSize());
        
        // 创建上传目录
        String uploadDir = uploadBasePath + File.separator + uploadId;
        try {
            Files.createDirectories(Paths.get(uploadDir));
        } catch (IOException e) {
            log.error("创建上传目录失败: {}", uploadDir, e);
            throw new AsrCoreException("创建上传目录失败");
        }
        
        // 创建上传信息
        ChunkUploadInfo uploadInfo = new ChunkUploadInfo();
        uploadInfo.setUploadId(uploadId);
        uploadInfo.setFileName(req.getFileName());
        uploadInfo.setFileSize(req.getFileSize());
        uploadInfo.setFileMd5(req.getFileMd5());
        uploadInfo.setChunkSize(req.getChunkSize());
        uploadInfo.setTotalChunks(totalChunks);
        uploadInfo.setUploadDir(uploadDir);
        uploadInfo.setCreateTime(new Date());
        uploadInfo.setBusinessId(req.getBusinessId());
        
        // 检查已存在的分块（用于断点续传）
        List<Integer> existingChunks = checkExistingChunks(uploadDir, totalChunks);
        existingChunks.forEach(uploadInfo.getUploadedChunks()::add);
        
        // 缓存上传信息
        uploadInfoCache.put(uploadId, uploadInfo);
        
        // 构建响应
        ChunkUploadInitResp resp = new ChunkUploadInitResp();
        resp.setUploadId(uploadId);
        resp.setTotalChunks(totalChunks);
        resp.setChunkSize(req.getChunkSize());
        resp.setExistingChunks(existingChunks);
        
        log.info("初始化分块上传成功: uploadId={}, fileName={}, totalChunks={}", 
                uploadId, req.getFileName(), totalChunks);
        
        return resp;
    }
    
    /**
     * 上传分块
     */
    public void uploadChunk(ChunkUploadReq req, MultipartFile chunkFile) {
        ChunkUploadInfo uploadInfo = uploadInfoCache.get(req.getUploadId());
        if (uploadInfo == null) {
            throw new AsrCoreException("上传任务不存在或已过期");
        }
        
        // 验证分块序号
        if (req.getChunkNumber() < 1 || req.getChunkNumber() > uploadInfo.getTotalChunks()) {
            throw new AsrCoreException("分块序号无效");
        }
        
        // 验证分块MD5
        try {
            String actualMd5 = DigestUtils.md5DigestAsHex(chunkFile.getInputStream());
            if (!actualMd5.equals(req.getChunkMd5())) {
                throw new AsrCoreException("分块MD5校验失败");
            }
        } catch (IOException e) {
            log.error("读取分块文件失败", e);
            throw new AsrCoreException("读取分块文件失败");
        }
        
        // 保存分块文件
        String chunkFileName = String.format("chunk_%d", req.getChunkNumber());
        Path chunkPath = Paths.get(uploadInfo.getUploadDir(), chunkFileName);
        
        try {
            chunkFile.transferTo(chunkPath.toFile());
            uploadInfo.getUploadedChunks().add(req.getChunkNumber());
            
            log.info("分块上传成功: uploadId={}, chunkNumber={}, progress={}/{}", 
                    req.getUploadId(), req.getChunkNumber(), 
                    uploadInfo.getUploadedChunks().size(), uploadInfo.getTotalChunks());
                    
        } catch (IOException e) {
            log.error("保存分块文件失败: {}", chunkPath, e);
            throw new AsrCoreException("保存分块文件失败");
        }
    }
    
    /**
     * 合并分块文件
     */
    public ChunkMergeResp mergeChunks(ChunkMergeReq req) {
        ChunkUploadInfo uploadInfo = uploadInfoCache.get(req.getUploadId());
        if (uploadInfo == null) {
            throw new AsrCoreException("上传任务不存在或已过期");
        }
        
        // 检查是否所有分块都已上传
        if (!uploadInfo.isAllChunksUploaded()) {
            throw new AsrCoreException("还有分块未上传完成");
        }
        
        // 合并文件
        String mergedFilePath = uploadInfo.getUploadDir() + File.separator + uploadInfo.getFileName();
        
        try (FileOutputStream fos = new FileOutputStream(mergedFilePath);
             BufferedOutputStream bos = new BufferedOutputStream(fos)) {
            
            for (int i = 1; i <= uploadInfo.getTotalChunks(); i++) {
                String chunkFileName = String.format("chunk_%d", i);
                Path chunkPath = Paths.get(uploadInfo.getUploadDir(), chunkFileName);
                
                try (FileInputStream fis = new FileInputStream(chunkPath.toFile());
                     BufferedInputStream bis = new BufferedInputStream(fis)) {
                    
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = bis.read(buffer)) != -1) {
                        bos.write(buffer, 0, bytesRead);
                    }
                }
            }
            
        } catch (IOException e) {
            log.error("合并分块文件失败: {}", mergedFilePath, e);
            throw new AsrCoreException("合并分块文件失败");
        }
        
        // 验证合并后文件的MD5
        try {
            String actualMd5 = calculateFileMd5(mergedFilePath);
            if (!actualMd5.equals(req.getFileMd5())) {
                // 删除错误的合并文件
                Files.deleteIfExists(Paths.get(mergedFilePath));
                throw new AsrCoreException("文件MD5校验失败");
            }
        } catch (IOException e) {
            log.error("计算文件MD5失败: {}", mergedFilePath, e);
            throw new AsrCoreException("文件校验失败");
        }
        
        // 清理分块文件
        cleanupChunkFiles(uploadInfo);
        
        // 从缓存中移除上传信息
        uploadInfoCache.remove(req.getUploadId());
        
        // 构建响应
        ChunkMergeResp resp = new ChunkMergeResp();
        resp.setFilePath(mergedFilePath);
        resp.setFileName(uploadInfo.getFileName());
        resp.setFileSize(uploadInfo.getFileSize());
        
        log.info("分块文件合并成功: uploadId={}, filePath={}", req.getUploadId(), mergedFilePath);
        
        return resp;
    }
    
    /**
     * 检查已存在的分块（用于断点续传）
     */
    private List<Integer> checkExistingChunks(String uploadDir, int totalChunks) {
        List<Integer> existingChunks = new ArrayList<>();
        
        for (int i = 1; i <= totalChunks; i++) {
            String chunkFileName = String.format("chunk_%d", i);
            Path chunkPath = Paths.get(uploadDir, chunkFileName);
            if (Files.exists(chunkPath)) {
                existingChunks.add(i);
            }
        }
        
        return existingChunks;
    }
    
    /**
     * 计算文件MD5
     */
    private String calculateFileMd5(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath)) {
            return DigestUtils.md5DigestAsHex(fis);
        }
    }
    
    /**
     * 清理分块文件
     */
    private void cleanupChunkFiles(ChunkUploadInfo uploadInfo) {
        try {
            for (int i = 1; i <= uploadInfo.getTotalChunks(); i++) {
                String chunkFileName = String.format("chunk_%d", i);
                Path chunkPath = Paths.get(uploadInfo.getUploadDir(), chunkFileName);
                Files.deleteIfExists(chunkPath);
            }
        } catch (IOException e) {
            log.warn("清理分块文件失败: {}", uploadInfo.getUploadDir(), e);
        }
    }
    
    /**
     * 获取上传进度
     */
    public double getUploadProgress(String uploadId) {
        ChunkUploadInfo uploadInfo = uploadInfoCache.get(uploadId);
        if (uploadInfo == null) {
            return 0.0;
        }
        
        return (double) uploadInfo.getUploadedChunks().size() / uploadInfo.getTotalChunks() * 100;
    }
}
