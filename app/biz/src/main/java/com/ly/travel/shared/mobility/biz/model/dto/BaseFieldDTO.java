package com.ly.travel.shared.mobility.biz.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.flight.toolkit.annoations.Diff;
import lombok.Data;

import java.util.Date;

@Data
public class BaseFieldDTO {
    /**
     * 主键id
     */
    @Diff(ignore = true)
    private Long id;

    /**
     * 环境
     */
    @Diff(ignore = true)
    private String env;

    /**
     * 创建人
     */
    @Diff(ignore = true)
    private String createBy;

    /**
     * 操作人
     */
    @Diff(ignore = true)
    private String updateBy;

    /**
     * 创建时间
     */
    @Diff(ignore = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改时间
     */
    @Diff(ignore = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 新增编辑的时候,是否校验已存在
     */
    @Diff(ignore = true)
    private boolean checkExists = true;
}
